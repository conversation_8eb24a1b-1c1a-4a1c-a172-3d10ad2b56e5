#!/bin/bash

# 订单创建接口测试脚本
# 使用方法: ./test_order_api.sh

BASE_URL="http://localhost:8080"
TOKEN=""

echo "=== 订单创建接口测试 ==="

# 1. 测试用户登录获取token (需要根据实际情况调整)
echo "1. 获取用户token..."
LOGIN_RESPONSE=$(curl -s -X POST "${BASE_URL}/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }')

echo "登录响应: $LOGIN_RESPONSE"

# 提取token (需要根据实际响应格式调整)
TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)
echo "Token: $TOKEN"

if [ -z "$TOKEN" ]; then
    echo "获取token失败，请检查登录接口"
    exit 1
fi

# 2. 测试获取商品列表
echo -e "\n2. 获取商品列表..."
PRODUCTS_RESPONSE=$(curl -s -X GET "${BASE_URL}/app/mall/products" \
  -H "Authorization: Bearer $TOKEN")

echo "商品列表响应: $PRODUCTS_RESPONSE"

# 3. 测试获取用户地址列表
echo -e "\n3. 获取用户地址列表..."
ADDRESS_RESPONSE=$(curl -s -X GET "${BASE_URL}/app/address/list" \
  -H "Authorization: Bearer $TOKEN")

echo "地址列表响应: $ADDRESS_RESPONSE"

# 4. 测试创建订单 - 成功案例
echo -e "\n4. 测试创建订单 - 成功案例..."
ORDER_REQUEST='{
  "addressId": 1,
  "deliveryFee": 10.00,
  "remark": "测试订单，请尽快发货",
  "orderItems": [
    {
      "productId": 1,
      "specId": null,
      "quantity": 1
    }
  ]
}'

CREATE_ORDER_RESPONSE=$(curl -s -X POST "${BASE_URL}/app/order/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "$ORDER_REQUEST")

echo "创建订单响应: $CREATE_ORDER_RESPONSE"

# 5. 测试创建订单 - 缺少地址ID
echo -e "\n5. 测试创建订单 - 缺少地址ID..."
ORDER_REQUEST_NO_ADDRESS='{
  "deliveryFee": 10.00,
  "remark": "测试订单",
  "orderItems": [
    {
      "productId": 1,
      "specId": null,
      "quantity": 1
    }
  ]
}'

CREATE_ORDER_RESPONSE_NO_ADDRESS=$(curl -s -X POST "${BASE_URL}/app/order/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "$ORDER_REQUEST_NO_ADDRESS")

echo "缺少地址ID响应: $CREATE_ORDER_RESPONSE_NO_ADDRESS"

# 6. 测试创建订单 - 空商品列表
echo -e "\n6. 测试创建订单 - 空商品列表..."
ORDER_REQUEST_NO_ITEMS='{
  "addressId": 1,
  "deliveryFee": 10.00,
  "remark": "测试订单",
  "orderItems": []
}'

CREATE_ORDER_RESPONSE_NO_ITEMS=$(curl -s -X POST "${BASE_URL}/app/order/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "$ORDER_REQUEST_NO_ITEMS")

echo "空商品列表响应: $CREATE_ORDER_RESPONSE_NO_ITEMS"

# 7. 测试创建订单 - 包含规格的商品
echo -e "\n7. 测试创建订单 - 包含规格的商品..."
ORDER_REQUEST_WITH_SPEC='{
  "addressId": 1,
  "deliveryFee": 5.00,
  "remark": "测试规格商品订单",
  "orderItems": [
    {
      "productId": 1,
      "specId": 1,
      "quantity": 2
    },
    {
      "productId": 2,
      "specId": null,
      "quantity": 1
    }
  ]
}'

CREATE_ORDER_RESPONSE_WITH_SPEC=$(curl -s -X POST "${BASE_URL}/app/order/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "$ORDER_REQUEST_WITH_SPEC")

echo "包含规格商品响应: $CREATE_ORDER_RESPONSE_WITH_SPEC"

# 8. 测试获取订单列表
echo -e "\n8. 获取订单列表..."
ORDER_LIST_RESPONSE=$(curl -s -X GET "${BASE_URL}/app/order/list" \
  -H "Authorization: Bearer $TOKEN")

echo "订单列表响应: $ORDER_LIST_RESPONSE"

echo -e "\n=== 测试完成 ==="
echo "请检查以上响应结果，确认接口功能是否正常"
