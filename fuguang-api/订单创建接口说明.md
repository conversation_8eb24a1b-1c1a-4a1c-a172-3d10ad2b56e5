# 订单创建接口说明

## 概述

本文档说明了APP端订单创建接口的完善情况和使用方法。

## 接口地址

```
POST /app/order/create
```

## 请求参数

### 请求体 (CreateOrderRequest)

```json
{
  "addressId": 1,
  "deliveryFee": 10.00,
  "remark": "请尽快发货",
  "orderItems": [
    {
      "productId": 1,
      "specId": 1,
      "quantity": 2
    },
    {
      "productId": 2,
      "specId": null,
      "quantity": 1
    }
  ]
}
```

### 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| addressId | Long | 是 | 收货地址ID |
| deliveryFee | BigDecimal | 否 | 配送费，默认为0 |
| remark | String | 否 | 订单备注 |
| orderItems | Array | 是 | 订单商品列表 |

### orderItems 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| productId | Long | 是 | 商品ID |
| specId | Long | 否 | 商品规格ID，如果商品有规格则必填 |
| quantity | Integer | 是 | 购买数量，必须大于0 |

## 响应结果

### 成功响应

```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "orderId": 1001,
    "orderNo": "FG1642567890001234",
    "userId": 1,
    "addressId": 1,
    "totalAmount": 120.00,
    "payAmount": 130.00,
    "orderStatus": "0",
    "payStatus": "0",
    "deliveryFee": 10.00,
    "receiverName": "张三",
    "receiverPhone": "13800138000",
    "receiverAddress": "北京市朝阳区xxx街道xxx号",
    "createTime": "2025-01-22 10:30:00",
    "orderItems": [
      {
        "itemId": 1,
        "orderId": 1001,
        "productId": 1,
        "specId": 1,
        "productName": "测试商品",
        "specName": "红色-L码",
        "productImage": "/upload/product1.jpg",
        "specImage": "/upload/spec1.jpg",
        "productPrice": 50.00,
        "quantity": 2,
        "totalPrice": 100.00
      }
    ]
  }
}
```

### 错误响应

```json
{
  "code": 500,
  "msg": "订单创建失败：商品库存不足：测试商品(红色-L码)"
}
```

## 业务逻辑

### 订单创建流程

1. **参数验证**
   - 验证收货地址ID是否存在
   - 验证订单商品列表不能为空
   - 验证每个商品的基本信息

2. **地址验证**
   - 验证收货地址是否存在
   - 验证用户是否有权使用该地址

3. **商品验证**
   - 验证商品是否存在且已上架
   - 如果有规格，验证规格是否存在且已上架
   - 验证库存是否充足

4. **金额计算**
   - 根据商品价格和数量计算小计
   - 计算订单总金额（商品金额 + 配送费）

5. **订单创建**
   - 生成订单号
   - 保存订单主信息
   - 保存订单项信息
   - 扣减商品/规格库存

6. **异常处理**
   - 任何步骤失败都会回滚事务
   - 返回具体的错误信息

### 库存管理

- **商品库存**：如果商品没有规格，直接扣减商品表的库存
- **规格库存**：如果商品有规格，扣减规格表的库存
- **库存恢复**：订单取消时会自动恢复对应的库存

### 订单状态

- **0**：待付款
- **1**：已付款
- **2**：已发货
- **3**：已完成
- **4**：已取消
- **5**：已退款

### 支付状态

- **0**：未支付
- **1**：已支付
- **2**：支付失败

## 注意事项

1. 接口需要用户登录，会自动获取当前用户ID
2. 订单创建成功后状态为"待付款"
3. 库存扣减是实时的，创建订单时就会扣减
4. 如果商品有多个规格，每个规格都需要单独作为订单项
5. 配送费可以为0，但不能为负数
6. 订单号格式：FG + 时间戳 + 4位随机数

## 相关接口

- 获取用户地址列表：`GET /app/address/list`
- 获取商品详情：`GET /app/mall/product/{productId}`
- 获取商品规格：`GET /app/mall/product/{productId}/specs`
- 创建支付订单：`POST /app/order/{orderId}/pay`
- 查询订单列表：`GET /app/order/list`
